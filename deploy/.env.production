# Monika 生产环境配置文件 - 本地部署版本

# Docker 镜像配置（本地）
DOCKER_REGISTRY=localhost
IMAGE_TAG=latest

# 应用安全配置
SECRET_KEY=c8ce78b69ed02d5129e9939ced2bdd7f11cd697ca6b744352b2e1fab7206c38f
ACCESS_TOKEN_EXPIRE_MINUTES=1440

# 数据库配置
DATABASE_URL=sqlite:///./data/monika.db

# 服务器配置（本地）
PRODUCTION_SERVER=localhost
DEPLOY_USER=$USER
DEPLOY_PATH=$PWD/deploy-local

# 备份配置
BACKUP_RETENTION_DAYS=7

# 监控配置
ENABLE_LOGGING=true
LOG_LEVEL=INFO
