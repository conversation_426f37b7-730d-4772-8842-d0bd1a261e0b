# Monika 生产环境配置文件
# 复制此文件为 .env.production 并修改相应配置

# Docker 镜像配置
DOCKER_REGISTRY=docker.io/your-username
IMAGE_TAG=latest

# 应用安全配置
SECRET_KEY=your-super-secret-key-here-change-this-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=1440

# 数据库配置
DATABASE_URL=sqlite:///./data/monika.db

# 服务器配置
PRODUCTION_SERVER=your-production-server.com
DEPLOY_USER=deploy
DEPLOY_PATH=/opt/monika

# Docker Hub 认证（如果使用私有仓库）
DOCKER_USERNAME=your-docker-username
DOCKER_PASSWORD=your-docker-password

# SSL 配置（如果启用 HTTPS）
SSL_CERT_PATH=/etc/ssl/certs/monika.crt
SSL_KEY_PATH=/etc/ssl/private/monika.key

# 备份配置
BACKUP_RETENTION_DAYS=7
BACKUP_SCHEDULE="0 2 * * *"  # 每天凌晨2点备份

# 监控配置
ENABLE_LOGGING=true
LOG_LEVEL=INFO

# 邮件通知配置（可选）
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
NOTIFICATION_EMAIL=<EMAIL>
