services:
  # 后端服务
  backend:
    image: monika-backend:latest
    container_name: monika-backend-local
    restart: unless-stopped
    environment:
      - PYTHONPATH=/app
      - DATABASE_URL=sqlite:///./data/monika.db
      - SECRET_KEY=local-development-secret-key-change-in-production
      - ACCESS_TOKEN_EXPIRE_MINUTES=1440
      - ENVIRONMENT=local
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    ports:
      - "8000:8000"
    networks:
      - monika-network
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 前端服务
  frontend:
    image: monika-frontend:latest
    container_name: monika-frontend-local
    restart: unless-stopped
    ports:
      - "8080:80"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - monika-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  monika-network:
    driver: bridge

volumes:
  monika-data:
    driver: local
