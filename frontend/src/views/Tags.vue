<template>
  <div class="tags-page">
    <div class="page-header">
      <h1>标签管理</h1>
      <p>管理您的交易标签，让记账更有条理</p>
    </div>
    
    <TagManager />
  </div>
</template>

<script setup>
import TagManager from '@/components/TagManager.vue'
</script>

<style scoped>
.tags-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}
</style>
