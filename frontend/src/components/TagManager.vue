<template>
  <div class="tag-manager">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>标签管理</span>
          <el-button type="primary" @click="showCreateDialog = true">
            <el-icon><Plus /></el-icon>
            新建标签
          </el-button>
        </div>
      </template>
      
      <el-table :data="tags" style="width: 100%" v-loading="loading">
        <el-table-column prop="name" label="标签名称" />
        <el-table-column prop="color" label="颜色" width="120">
          <template #default="scope">
            <div class="color-display">
              <div 
                class="color-box" 
                :style="{ backgroundColor: scope.row.color }"
              ></div>
              <span>{{ scope.row.color }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="editTag(scope.row)">编辑</el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="deleteTag(scope.row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 创建/编辑对话框 -->
    <el-dialog 
      v-model="showCreateDialog" 
      :title="editingTag ? '编辑标签' : '创建标签'"
      width="400px"
    >
      <el-form :model="tagForm" label-width="80px" :rules="rules" ref="tagFormRef">
        <el-form-item label="标签名称" prop="name">
          <el-input v-model="tagForm.name" placeholder="请输入标签名称" />
        </el-form-item>
        <el-form-item label="颜色" prop="color">
          <el-color-picker v-model="tagForm.color" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="cancelEdit">取消</el-button>
        <el-button type="primary" @click="saveTag" :loading="saving">
          {{ editingTag ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import api from '@/api'

// 响应式数据
const tags = ref([])
const loading = ref(false)
const saving = ref(false)
const showCreateDialog = ref(false)
const editingTag = ref(null)
const tagFormRef = ref()

// 表单数据
const tagForm = reactive({
  name: '',
  color: '#007bff'
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入标签名称', trigger: 'blur' },
    { min: 1, max: 50, message: '标签名称长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  color: [
    { required: true, message: '请选择颜色', trigger: 'change' }
  ]
}

// 获取标签列表
const fetchTags = async () => {
  loading.value = true
  try {
    const response = await api.get('/tags/')
    tags.value = response.data
  } catch (error) {
    ElMessage.error('获取标签列表失败')
    console.error('Error fetching tags:', error)
  } finally {
    loading.value = false
  }
}

// 创建或更新标签
const saveTag = async () => {
  if (!tagFormRef.value) return
  
  try {
    await tagFormRef.value.validate()
  } catch (error) {
    return
  }

  saving.value = true
  try {
    if (editingTag.value) {
      // 更新标签
      await api.put(`/tags/${editingTag.value.id}`, tagForm)
      ElMessage.success('标签更新成功')
    } else {
      // 创建标签
      await api.post('/tags/', tagForm)
      ElMessage.success('标签创建成功')
    }
    
    showCreateDialog.value = false
    await fetchTags()
  } catch (error) {
    const message = error.response?.data?.detail || '操作失败'
    ElMessage.error(message)
  } finally {
    saving.value = false
  }
}

// 编辑标签
const editTag = (tag) => {
  editingTag.value = tag
  tagForm.name = tag.name
  tagForm.color = tag.color
  showCreateDialog.value = true
}

// 删除标签
const deleteTag = async (tagId) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个标签吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    await api.delete(`/tags/${tagId}`)
    ElMessage.success('标签删除成功')
    await fetchTags()
  } catch (error) {
    if (error !== 'cancel') {
      const message = error.response?.data?.detail || '删除失败'
      ElMessage.error(message)
    }
  }
}

// 取消编辑
const cancelEdit = () => {
  showCreateDialog.value = false
  editingTag.value = null
  tagForm.name = ''
  tagForm.color = '#007bff'
  if (tagFormRef.value) {
    tagFormRef.value.clearValidate()
  }
}

// 格式化日期
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchTags()
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.color-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-box {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}
</style>
