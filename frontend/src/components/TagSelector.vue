<template>
  <div class="tag-selector">
    <el-select
      v-model="selectedTagIds"
      multiple
      filterable
      placeholder="选择标签"
      style="width: 100%"
      @change="handleChange"
    >
      <el-option
        v-for="tag in tags"
        :key="tag.id"
        :label="tag.name"
        :value="tag.id"
      >
        <div class="tag-option">
          <div 
            class="tag-color" 
            :style="{ backgroundColor: tag.color }"
          ></div>
          <span>{{ tag.name }}</span>
        </div>
      </el-option>
    </el-select>
    
    <!-- 已选标签显示 -->
    <div class="selected-tags" v-if="selectedTags.length">
      <el-tag
        v-for="tag in selectedTags"
        :key="tag.id"
        :color="tag.color"
        closable
        @close="removeTag(tag.id)"
        class="selected-tag"
      >
        {{ tag.name }}
      </el-tag>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import api from '@/api'

// Props
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const tags = ref([])
const selectedTagIds = ref([])

// 计算属性
const selectedTags = computed(() => {
  return tags.value.filter(tag => selectedTagIds.value.includes(tag.id))
})

// 监听 modelValue 变化
watch(() => props.modelValue, (newValue) => {
  selectedTagIds.value = newValue || []
}, { immediate: true })

// 获取标签列表
const fetchTags = async () => {
  try {
    const response = await api.get('/tags/')
    tags.value = response.data
  } catch (error) {
    ElMessage.error('获取标签列表失败')
    console.error('Error fetching tags:', error)
  }
}

// 处理选择变化
const handleChange = (value) => {
  selectedTagIds.value = value
  emit('update:modelValue', value)
}

// 移除标签
const removeTag = (tagId) => {
  const index = selectedTagIds.value.indexOf(tagId)
  if (index > -1) {
    selectedTagIds.value.splice(index, 1)
    emit('update:modelValue', selectedTagIds.value)
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchTags()
})

// 暴露方法给父组件
defineExpose({
  fetchTags
})
</script>

<style scoped>
.tag-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tag-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 1px solid #dcdfe6;
}

.selected-tags {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.selected-tag {
  margin-right: 0;
}
</style>
