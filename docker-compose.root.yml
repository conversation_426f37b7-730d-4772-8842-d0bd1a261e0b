services:
  # 后端服务 (root 用户)
  backend:
    image: monika-backend:latest
    container_name: monika-backend-root
    restart: unless-stopped
    user: "0:0"  # root 用户
    environment:
      - PYTHONPATH=/app
      - DATABASE_URL=sqlite:///./data/monika.db
      - SECRET_KEY=local-development-secret-key-change-in-production
      - ACCESS_TOKEN_EXPIRE_MINUTES=1440
      - ENVIRONMENT=local
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    ports:
      - "8000:8000"
    networks:
      - monika-network

  # 前端服务
  frontend:
    image: monika-frontend:latest
    container_name: monika-frontend-root
    restart: unless-stopped
    ports:
      - "8080:80"
    depends_on:
      - backend
    networks:
      - monika-network

networks:
  monika-network:
    driver: bridge
