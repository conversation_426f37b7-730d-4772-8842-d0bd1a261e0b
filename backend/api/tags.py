from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from database.database import get_db
from auth.auth import get_current_user
from models.models import User, Tag as TagModel
from schemas.schemas import Tag, TagCreate, TagUpdate
from crud import crud

router = APIRouter(
    prefix="/tags",
    tags=["tags"],
    responses={404: {"description": "Not found"}},
)


@router.post("/", response_model=Tag, status_code=status.HTTP_201_CREATED)
def create_tag(
    tag: TagCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建新标签"""
    # 检查标签名是否已存在
    existing_tag = db.query(TagModel).filter(
        TagModel.user_id == current_user.id,
        TagModel.name == tag.name
    ).first()
    
    if existing_tag:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Tag with this name already exists"
        )
    
    return crud.create_tag(db=db, tag=tag, user_id=current_user.id)


@router.get("/", response_model=List[Tag])
def read_tags(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取用户的所有标签"""
    return crud.get_tags(db, user_id=current_user.id, skip=skip, limit=limit)


@router.get("/{tag_id}", response_model=Tag)
def read_tag(
    tag_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取特定标签"""
    tag = crud.get_tag(db, tag_id=tag_id, user_id=current_user.id)
    if tag is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tag not found"
        )
    return tag


@router.put("/{tag_id}", response_model=Tag)
def update_tag(
    tag_id: int,
    tag_update: TagUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新标签"""
    # 检查标签是否存在
    existing_tag = crud.get_tag(db, tag_id=tag_id, user_id=current_user.id)
    if existing_tag is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tag not found"
        )
    
    # 如果更新名称，检查新名称是否已存在
    if tag_update.name and tag_update.name != existing_tag.name:
        name_exists = db.query(TagModel).filter(
            TagModel.user_id == current_user.id,
            TagModel.name == tag_update.name,
            TagModel.id != tag_id
        ).first()
        
        if name_exists:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Tag with this name already exists"
            )
    
    updated_tag = crud.update_tag(
        db=db, tag_id=tag_id, user_id=current_user.id, tag_update=tag_update
    )
    return updated_tag


@router.delete("/{tag_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_tag(
    tag_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除标签"""
    success = crud.delete_tag(db=db, tag_id=tag_id, user_id=current_user.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tag not found"
        )


@router.post("/transactions/{transaction_id}/tags/", response_model=dict)
def add_tags_to_transaction(
    transaction_id: int,
    tag_ids: List[int],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """为交易添加标签"""
    # 验证交易是否存在且属于当前用户
    transaction = crud.get_transaction(db, transaction_id=transaction_id, user_id=current_user.id)
    if not transaction:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Transaction not found"
        )
    
    # 验证所有标签都属于当前用户
    tags = db.query(TagModel).filter(
        TagModel.id.in_(tag_ids),
        TagModel.user_id == current_user.id
    ).all()
    
    if len(tags) != len(tag_ids):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="One or more tags not found or don't belong to user"
        )
    
    # 添加标签到交易
    transaction.tags = tags
    db.commit()
    
    return {"message": "Tags added to transaction successfully"}


@router.delete("/transactions/{transaction_id}/tags/{tag_id}", status_code=status.HTTP_204_NO_CONTENT)
def remove_tag_from_transaction(
    transaction_id: int,
    tag_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """从交易中移除标签"""
    # 验证交易是否存在且属于当前用户
    transaction = crud.get_transaction(db, transaction_id=transaction_id, user_id=current_user.id)
    if not transaction:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Transaction not found"
        )
    
    # 验证标签是否属于当前用户
    tag = crud.get_tag(db, tag_id=tag_id, user_id=current_user.id)
    if not tag:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tag not found"
        )
    
    # 从交易中移除标签
    if tag in transaction.tags:
        transaction.tags.remove(tag)
        db.commit()
